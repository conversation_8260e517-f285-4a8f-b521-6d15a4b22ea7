<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>采购计划管理 - 列表</title>
    <!-- CSS文件已在主页面(index.html)中统一加载，无需在模板中重复引入 -->
</head>

<body>

    <div id="app" class="container">
        <div class="container-header">
            <div class="header-left">
                <div class="header-title">采购计划管理</div>
            </div>
            <div class="header-right">
                <button class="button primary" onclick="window.addNew()">新建</button>
            </div>
        </div>

        <div class="tabs">
            <div class="tab-item active" data-tab="pending">待办</div>
            <div class="tab-item" data-tab="completed">已办</div>
        </div>

        <div class="search-area">
            <div class="search-form">
                <div class="search-form-item">
                    <label>计划项目名称:</label>
                    <input type="text" id="planProjectName" placeholder="2位以上进行模糊查询">
                </div>
                <div class="search-form-item">
                    <label>审核状态:</label>
                    <select id="auditStatus" multiple>
                        <option value="待审核">待审核</option>
                        <option value="审核中">审核中</option>
                        <option value="审核通过">审核通过</option>
                        <option value="审核未过">审核未过</option>
                    </select>
                </div>
                <div class="search-form-item">
                    <label>采购类型:</label>
                    <select id="procurementType" multiple>
                        <option value="货物">货物</option>
                        <option value="施工">施工</option>
                        <option value="服务">服务</option>
                        <option value="其他">其他</option>
                    </select>
                </div>
                <div class="search-form-item">
                    <label>采购方式:</label>
                    <select id="procurementMethod" multiple>
                        <option value="公告比选">公告比选</option>
                        <option value="邀请比选">邀请比选</option>
                        <option value="竞争性磋商">竞争性磋商</option>
                        <option value="竞争性谈判">竞争性谈判</option>
                        <option value="询价择优">询价择优</option>
                        <option value="单一来源">单一来源</option>
                    </select>
                </div>
                <div class="search-form-item">
                    <button class="button primary" onclick="searchData()">查询</button>
                    <button class="button" onclick="resetSearch()">重置</button>
                    <button class="button" onclick="toggleAdvancedSearch()">高级查询</button>
                </div>
            </div>

            <!-- 高级查询区域 -->
            <div class="search-form advanced-search" id="advancedSearch" style="display: none;">
                <div class="search-form-item">
                    <label>采购组织方式:</label>
                    <select id="procurementOrganizationMethod" multiple>
                        <option value="自主招标">自主招标</option>
                        <option value="委托招标">委托招标</option>
                    </select>
                </div>
                <div class="search-form-item">
                    <label>资金来源:</label>
                    <select id="fundSource" multiple>
                        <option value="自有资金">自有资金</option>
                        <option value="政府资本">政府资本</option>
                        <option value="其他社会资本">其他社会资本</option>
                    </select>
                </div>
                <div class="search-form-item">
                    <label>采购预算金额(万元):</label>
                    <input type="number" id="budgetAmountMin" placeholder="最小值">
                    <span>-</span>
                    <input type="number" id="budgetAmountMax" placeholder="最大值">
                </div>
                <div class="search-form-item">
                    <label>项目经办人:</label>
                    <input type="text" id="projectHandler" placeholder="2位以上进行模糊查询">
                </div>
                <div class="search-form-item">
                    <label>立项决策日期:</label>
                    <input type="date" id="decisionDateStart">
                    <span>-</span>
                    <input type="date" id="decisionDateEnd">
                </div>
                <div class="search-form-item">
                    <label>创建时间:</label>
                    <input type="date" id="createTimeStart">
                    <span>-</span>
                    <input type="date" id="createTimeEnd">
                </div>
            </div>
        </div>

        <div class="container-table">
            <div class="table-container">
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>计划编号</th>
                            <th>计划项目名称</th>
                            <th>采购类型</th>
                            <th>采购方式</th>
                            <th>审核状态</th>
                            <th>采购组织方式</th>
                            <th>代理机构</th>
                            <th>项目经办人</th>
                            <th>项目业主</th>
                            <th>招标时间</th>
                            <th>创建时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="dataTableBody">
                        <!-- 数据将通过JavaScript动态加载 -->
                    </tbody>
                </table>
            </div>
        </div>

        <div class="pagination-container" id="pagination">
            <!-- 分页组件将通过JavaScript动态生成 -->
        </div>
    </div>

    <script>
        // ==================== iframe兼容的状态管理 ====================
        window.pageState = window.pageState || {
            isInitialized: false,
            currentTab: 'pending',
            currentPage: 1,
            pageSize: 10,
            searchParams: {}
        };

        function resetPageState() {
            window.pageState.isInitialized = false;
            window.pageState.currentTab = 'pending';
            window.pageState.currentPage = 1;
            window.pageState.pageSize = 10;
            window.pageState.searchParams = {};
        }

        // ==================== 全局函数定义 ====================
        window.addNew = function() {
            window.top.location.hash = '#/procurement-plan/add-edit';
        };

        window.viewDetail = function(id) {
            window.top.location.hash = `#/procurement-plan/detail-review?id=${id}`;
        };

        window.editItem = function(id) {
            window.top.location.hash = `#/procurement-plan/add-edit?id=${id}`;
        };

        window.deleteItem = function(id) {
            if (confirm('确定要删除这条记录吗？')) {
                // 实际删除逻辑
                alert('删除成功！');
                loadData();
            }
        };

        window.submitItem = function(id) {
            if (confirm('确定要提交这条记录吗？')) {
                // 实际提交逻辑
                alert('提交成功！');
                loadData();
            }
        };

        window.reviewItem = function(id) {
            window.top.location.hash = `#/procurement-plan/detail-review?id=${id}&review=1`;
        };

        // ==================== 页面功能函数 ====================
        function searchData() {
            window.pageState.currentPage = 1;
            loadData();
        }

        function resetSearch() {
            // 重置所有搜索表单
            document.getElementById('planProjectName').value = '';
            document.getElementById('auditStatus').selectedIndex = -1;
            document.getElementById('procurementType').selectedIndex = -1;
            document.getElementById('procurementMethod').selectedIndex = -1;
            
            // 重置高级搜索
            document.getElementById('procurementOrganizationMethod').selectedIndex = -1;
            document.getElementById('fundSource').selectedIndex = -1;
            document.getElementById('budgetAmountMin').value = '';
            document.getElementById('budgetAmountMax').value = '';
            document.getElementById('projectHandler').value = '';
            document.getElementById('decisionDateStart').value = '';
            document.getElementById('decisionDateEnd').value = '';
            document.getElementById('createTimeStart').value = '';
            document.getElementById('createTimeEnd').value = '';
            
            loadData();
        }

        function toggleAdvancedSearch() {
            const advancedSearch = document.getElementById('advancedSearch');
            if (advancedSearch.style.display === 'none') {
                advancedSearch.style.display = 'flex';
            } else {
                advancedSearch.style.display = 'none';
            }
        }

        function loadData() {
            // 模拟数据加载
            const mockData = [
                {
                    id: '***********-0001',
                    planNumber: '***********-0001',
                    planProjectName: '某公司2024年度办公用品采购计划',
                    procurementType: '货物',
                    procurementMethod: '公告比选',
                    auditStatus: '待审核',
                    procurementOrganizationMethod: '自主招标',
                    agency: '无',
                    projectHandler: '张三',
                    projectOwner: '行政部',
                    biddingTime: '2024年第三季度',
                    createTime: '2024-08-01 10:00:00'
                },
                {
                    id: '***********-0002',
                    planNumber: '***********-0002',
                    planProjectName: '某公司2024年度IT设备采购计划',
                    procurementType: '货物',
                    procurementMethod: '竞争性磋商',
                    auditStatus: '审核中',
                    procurementOrganizationMethod: '委托招标',
                    agency: '代理机构A',
                    projectHandler: '李四',
                    projectOwner: 'IT部',
                    biddingTime: '2024年第四季度',
                    createTime: '2024-08-02 14:30:00'
                }
            ];

            const tbody = document.getElementById('dataTableBody');
            tbody.innerHTML = '';

            mockData.forEach(item => {
                const row = document.createElement('tr');
                
                // 根据审核状态确定操作按钮
                let actionButtons = '';
                if (item.auditStatus === '待审核') {
                    actionButtons = `
                        <button class="button text" onclick="window.viewDetail('${item.id}')">详情</button>
                        <button class="button text" onclick="window.editItem('${item.id}')">编辑</button>
                        <button class="button text" onclick="window.deleteItem('${item.id}')">删除</button>
                        <button class="button text" onclick="window.submitItem('${item.id}')">提交</button>
                    `;
                } else if (item.auditStatus === '审核中') {
                    actionButtons = `
                        <button class="button text" onclick="window.viewDetail('${item.id}')">详情</button>
                        <button class="button text" onclick="window.reviewItem('${item.id}')">审核</button>
                    `;
                } else {
                    actionButtons = `
                        <button class="button text" onclick="window.viewDetail('${item.id}')">详情</button>
                    `;
                }

                row.innerHTML = `
                    <td>${item.planNumber}</td>
                    <td>${item.planProjectName}</td>
                    <td>${item.procurementType}</td>
                    <td>${item.procurementMethod}</td>
                    <td>${item.auditStatus}</td>
                    <td>${item.procurementOrganizationMethod}</td>
                    <td>${item.agency}</td>
                    <td>${item.projectHandler}</td>
                    <td>${item.projectOwner}</td>
                    <td>${item.biddingTime}</td>
                    <td>${item.createTime}</td>
                    <td class="action-buttons">${actionButtons}</td>
                `;
                tbody.appendChild(row);
            });
        }

        function initTabSwitching() {
            const tabItems = document.querySelectorAll('.funi-tabs .tab-item');
            
            tabItems.forEach(item => {
                item.addEventListener('click', () => {
                    // 移除所有active类
                    tabItems.forEach(tab => tab.classList.remove('active'));
                    // 添加active类到当前点击的tab
                    item.classList.add('active');
                    
                    // 更新当前tab状态
                    window.pageState.currentTab = item.dataset.tab;
                    
                    // 重新加载数据
                    loadData();
                });
            });
        }

        function initPageData() {
            resetPageState();
            window.pageState.isInitialized = true;
            
            initTabSwitching();
            loadData();
        }

        // ==================== 页面初始化 ====================
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', initPageData);
        } else {
            initPageData();
        }
        window.addEventListener('load', initPageData);
        setTimeout(initPageData, 100);
    </script>
</body>

</html>
