<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>采购计划管理 - 列表</title>
    <!-- CSS文件已在主页面(index.html)中统一加载，无需在模板中重复引入 -->
</head>

<body>

    <div id="app" class="container">
        <div class="container-header">
            <div class="tabs">
                <div class="tab-item active" data-tab="pending">待办</div>
                <div class="tab-item" data-tab="completed">已办</div>
            </div>

            <div class="search-area">
                <div class="search-form">
                    <div class="search-form-item">
                        <label>计划项目名称:</label>
                        <input type="text" id="planProjectName" placeholder="2位以上进行模糊查询">
                    </div>
                    <div class="search-form-item">
                        <label>审核状态:</label>
                        <select id="auditStatus">
                            <option value="">全部</option>
                            <option value="待审核">待审核</option>
                            <option value="审核中">审核中</option>
                            <option value="审核通过">审核通过</option>
                            <option value="审核未过">审核未过</option>
                        </select>
                    </div>
                    <div class="search-form-item">
                        <label>采购类型:</label>
                        <select id="procurementType">
                            <option value="">全部</option>
                            <option value="货物">货物</option>
                            <option value="施工">施工</option>
                            <option value="服务">服务</option>
                            <option value="其他">其他</option>
                        </select>
                    </div>
                    <div class="search-form-item">
                        <label>采购方式:</label>
                        <select id="procurementMethod">
                            <option value="">全部</option>
                            <option value="公告比选">公告比选</option>
                            <option value="邀请比选">邀请比选</option>
                            <option value="竞争性磋商">竞争性磋商</option>
                            <option value="竞争性谈判">竞争性谈判</option>
                            <option value="询价择优">询价择优</option>
                            <option value="单一来源">单一来源</option>
                        </select>
                    </div>
                    <div class="search-form-item">
                        <label>采购组织方式:</label>
                        <select id="procurementOrganizationMethod">
                            <option value="">全部</option>
                            <option value="自主招标">自主招标</option>
                            <option value="委托招标">委托招标</option>
                        </select>
                    </div>
                    <div class="search-form-item">
                        <label>立项决策日期:</label>
                        <input type="date" id="decisionDateStart">
                        <span>至</span>
                        <input type="date" id="decisionDateEnd">
                    </div>
                    <div class="search-form-item search-buttons-item">
                        <button class="button primary" onclick="searchData()">查询</button>
                        <button class="button" onclick="resetSearch()">重置</button>
                        <button class="button" onclick="exportData()">导出</button>
                    </div>
                </div>
            </div>
        </div>

        <div class="container-table">
            <div class="action-buttons">
                <button class="button primary" onclick="window.addNew()">新建</button>
                <button class="button" onclick="batchDelete()" disabled id="batchDeleteBtn">批量删除</button>
                <button class="button" onclick="batchExport()">批量导出</button>
            </div>
            <div class="table-container">
                <table class="data-table">
                    <thead>
                        <tr>
                            <th width="50">
                                <input type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                            </th>
                            <th>计划编号</th>
                            <th>计划项目名称</th>
                            <th>审核状态</th>
                            <th>采购类型</th>
                            <th>采购方式</th>
                            <th>采购组织方式(万元)</th>
                            <th>项目经办人</th>
                            <th>立项决策日期</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="dataTableBody">
                        <!-- 数据将通过JavaScript动态加载 -->
                    </tbody>
                </table>
            </div>
        </div>

        <div class="pagination-container" id="pagination">
            <!-- 分页组件将通过JavaScript动态生成 -->
        </div>
    </div>

    <script>
        // ==================== iframe兼容的状态管理 ====================
        window.pageState = window.pageState || {
            isInitialized: false,
            currentTab: 'pending',
            currentPage: 1,
            pageSize: 10,
            searchParams: {}
        };

        function resetPageState() {
            window.pageState.isInitialized = false;
            window.pageState.currentTab = 'pending';
            window.pageState.currentPage = 1;
            window.pageState.pageSize = 10;
            window.pageState.searchParams = {};
        }

        // ==================== 全局函数定义 ====================
        window.addNew = function() {
            window.top.location.hash = '#/procurement-plan/add-edit';
        };

        window.viewDetail = function(id) {
            window.top.location.hash = `#/procurement-plan/detail-review?id=${id}`;
        };

        window.editItem = function(id) {
            window.top.location.hash = `#/procurement-plan/add-edit?id=${id}`;
        };

        window.deleteItem = function(id) {
            if (confirm('确定要删除这条记录吗？')) {
                // 实际删除逻辑
                alert('删除成功！');
                loadData();
            }
        };

        window.submitItem = function(id) {
            if (confirm('确定要提交这条记录吗？')) {
                // 实际提交逻辑
                alert('提交成功！');
                loadData();
            }
        };

        window.reviewItem = function(id) {
            window.top.location.hash = `#/procurement-plan/detail-review?id=${id}&review=1`;
        };

        // ==================== 页面功能函数 ====================
        function searchData() {
            window.pageState.currentPage = 1;
            loadData();
        }

        function resetSearch() {
            // 重置所有搜索表单
            document.getElementById('planProjectName').value = '';
            document.getElementById('auditStatus').selectedIndex = 0;
            document.getElementById('procurementType').selectedIndex = 0;
            document.getElementById('procurementMethod').selectedIndex = 0;
            document.getElementById('procurementOrganizationMethod').selectedIndex = 0;
            document.getElementById('decisionDateStart').value = '';
            document.getElementById('decisionDateEnd').value = '';

            loadData();
        }

        function exportData() {
            alert('导出功能开发中...');
        }

        // 批量操作功能
        function toggleSelectAll() {
            const selectAll = document.getElementById('selectAll');
            const checkboxes = document.querySelectorAll('tbody input[type="checkbox"]');
            checkboxes.forEach(checkbox => {
                checkbox.checked = selectAll.checked;
            });
            updateBatchButtons();
        }

        function updateBatchButtons() {
            const checkedBoxes = document.querySelectorAll('tbody input[type="checkbox"]:checked');
            const batchDeleteBtn = document.getElementById('batchDeleteBtn');
            batchDeleteBtn.disabled = checkedBoxes.length === 0;
        }

        function batchDelete() {
            const checkedBoxes = document.querySelectorAll('tbody input[type="checkbox"]:checked');
            if (checkedBoxes.length === 0) {
                alert('请选择要删除的记录');
                return;
            }
            if (confirm(`确定要删除选中的 ${checkedBoxes.length} 条记录吗？`)) {
                alert('批量删除成功！');
                loadData();
            }
        }

        function batchExport() {
            const checkedBoxes = document.querySelectorAll('tbody input[type="checkbox"]:checked');
            if (checkedBoxes.length === 0) {
                alert('请选择要导出的记录');
                return;
            }
            alert(`导出选中的 ${checkedBoxes.length} 条记录...`);
        }



        function loadData() {
            // 模拟数据加载
            const mockData = [
                {
                    id: '***********-0001',
                    planNumber: '***********-0001',
                    planProjectName: '某公司2024年度办公用品采购计划',
                    procurementType: '货物',
                    procurementMethod: '公告比选',
                    auditStatus: '待审核',
                    procurementOrganizationMethod: '自主招标',
                    agency: '无',
                    projectHandler: '张三',
                    projectOwner: '行政部',
                    biddingTime: '2024年第三季度',
                    createTime: '2024-08-01 10:00:00'
                },
                {
                    id: '***********-0002',
                    planNumber: '***********-0002',
                    planProjectName: '某公司2024年度IT设备采购计划',
                    procurementType: '货物',
                    procurementMethod: '竞争性磋商',
                    auditStatus: '审核中',
                    procurementOrganizationMethod: '委托招标',
                    agency: '代理机构A',
                    projectHandler: '李四',
                    projectOwner: 'IT部',
                    biddingTime: '2024年第四季度',
                    createTime: '2024-08-02 14:30:00'
                }
            ];

            const tbody = document.getElementById('dataTableBody');
            tbody.innerHTML = '';

            mockData.forEach(item => {
                const row = document.createElement('tr');
                
                // 根据审核状态确定操作按钮
                let actionButtons = '';
                if (item.auditStatus === '待审核') {
                    actionButtons = `
                        <button class="button text" onclick="window.viewDetail('${item.id}')">详情</button>
                        <button class="button text" onclick="window.editItem('${item.id}')">编辑</button>
                        <button class="button text" onclick="window.deleteItem('${item.id}')">删除</button>
                        <button class="button text" onclick="window.submitItem('${item.id}')">提交</button>
                    `;
                } else if (item.auditStatus === '审核中') {
                    actionButtons = `
                        <button class="button text" onclick="window.viewDetail('${item.id}')">详情</button>
                        <button class="button text" onclick="window.reviewItem('${item.id}')">审核</button>
                    `;
                } else {
                    actionButtons = `
                        <button class="button text" onclick="window.viewDetail('${item.id}')">详情</button>
                    `;
                }

                row.innerHTML = `
                    <td><input type="checkbox" onchange="updateBatchButtons()" data-id="${item.id}"></td>
                    <td>${item.planNumber}</td>
                    <td>${item.planProjectName}</td>
                    <td><span class="status-badge status-${item.auditStatus}">${item.auditStatus}</span></td>
                    <td>${item.procurementType}</td>
                    <td>${item.procurementMethod}</td>
                    <td>${item.procurementOrganizationMethod}</td>
                    <td>${item.projectHandler}</td>
                    <td>${item.biddingTime}</td>
                    <td class="action-buttons">${actionButtons}</td>
                `;
                tbody.appendChild(row);
            });
        }

        function initTabSwitching() {
            const tabItems = document.querySelectorAll('.funi-tabs .tab-item');
            
            tabItems.forEach(item => {
                item.addEventListener('click', () => {
                    // 移除所有active类
                    tabItems.forEach(tab => tab.classList.remove('active'));
                    // 添加active类到当前点击的tab
                    item.classList.add('active');
                    
                    // 更新当前tab状态
                    window.pageState.currentTab = item.dataset.tab;
                    
                    // 重新加载数据
                    loadData();
                });
            });
        }

        function initPageData() {
            resetPageState();
            window.pageState.isInitialized = true;
            
            initTabSwitching();
            loadData();
        }

        // ==================== 页面初始化 ====================
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', initPageData);
        } else {
            initPageData();
        }
        window.addEventListener('load', initPageData);
        setTimeout(initPageData, 100);
    </script>
</body>

</html>
